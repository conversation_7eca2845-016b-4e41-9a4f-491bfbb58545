import React from "react"
import { Menus } from "~/menus"

export const loadAllMenus = (allMenus: Menus[]) => {
  const menus: Menus[] = []
  const findChildrenMenu = (menu: Menus, parents: Set<React.Key>) => {
    if (parents) parents.add(menu.key)
    if (menu.path) {
      const newMenu = { ...menu }
      newMenu.parents = [...parents.keys()]
      menus.push(newMenu)
    } else if (menu.children) menu.children.forEach(value => findChildrenMenu(value, parents))
  }

  allMenus.forEach(value => findChildrenMenu(value, new Set()))
  return menus
}
