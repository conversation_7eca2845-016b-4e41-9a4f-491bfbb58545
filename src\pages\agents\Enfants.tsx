import { EditableProTable, ProCard, ProForm<PERSON>ield } from "@ant-design/pro-components"
import { Button } from "antd"
import { useState } from "react"

// const defaultData = new Array(5).fill(1).map((_, index) => {
//   return {
//     id: (Date.now() + index).toString(),
//     title: `titre ${index}`,
//     decs: "gescription",
//     state: "open",
//     created_at: 1590486176000,
//   }
// })
const defaultData = []

export default function Enfants(params) {
  const [editableKeys, setEditableRowKeys] = useState(() => defaultData.map(item => item.id))
  const [dataSource, setDataSource] = useState(() => defaultData)

  const columns = [
    {
      title: "title",
      dataIndex: "title",
      width: "30%",
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: "required",
          },
          {
            message: "number",
            pattern: /[0-9]/,
          },
          {
            max: 16,
            whitespace: true,
            message: "max 16",
          },
          {
            min: 6,
            whitespace: true,
            message: "min 6",
          },
        ],
      },
    },
    {
      title: "state",
      key: "state",
      dataIndex: "state",
      valueType: "select",
      valueEnum: {
        all: { text: "all", status: "Default" },
        open: {
          text: "open",
          status: "Error",
        },
        closed: {
          text: "closed",
          status: "Success",
        },
      },
    },
    {
      title: "decs",
      dataIndex: "decs",
    },
    {
      title: "option",
      valueType: "option",
      width: 250,
      render: () => {
        return null
      },
    },
  ]

  return (
    <>
      <EditableProTable
        headerTitle="Enfants"
        columns={columns}
        rowKey="id"
        scroll={{
          x: 960,
        }}
        value={dataSource}
        onChange={setDataSource}
        recordCreatorProps={{
          newRecordType: "dataSource",
          record: () => ({
            id: Date.now(),
          }),
        }}
        toolBarRender={() => {
          return [
            <Button
              type="primary"
              key="save"
              onClick={() => {
                console.log(dataSource)
              }}
            >
              load
            </Button>,
          ]
        }}
        editable={{
          type: "multiple",
          editableKeys,
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.delete]
          },
          onValuesChange: (record, recordList) => {
            setDataSource(recordList)
          },
          onChange: setEditableRowKeys,
        }}
      />
      <ProCard title="voir" headerBordered collapsible defaultCollapsed>
        <ProFormField
          ignoreFormItem
          fieldProps={{
            style: {
              width: "100%",
            },
          }}
          mode="read"
          valueType="jsonCode"
          text={JSON.stringify(dataSource)}
        />
      </ProCard>
    </>
  )
}
