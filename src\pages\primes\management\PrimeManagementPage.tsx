import { useEffect, useState } from "react";
import { PageContainer } from "@ant-design/pro-components";
import { 
  Button, 
  Typography, 
  Table, 
  Space, 
  Tag, 
  Modal, 
  message, 
  Form, 
  Input, 
  Select, 
  InputNumber,
  DatePicker,
  Switch
} from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import { Prime, PrimeStatus, PrimeType, Periodicite } from "../../../models/Prime";
import { deletePrime, getPrimes, createPrime, updatePrime } from "../../../services/api/primeService";
import { useNavigate } from "react-router";
import dayjs from "dayjs";

const { Option } = Select;

interface PrimeFormData {
  name: string;
  type: PrimeType;
  amount: number;
  date: string;
  status: PrimeStatus;
  periodicite?: Periodicite;
  avec_unite?: boolean;
  description?: string;
  periode?: string;
}

const PrimeManagementPage = () => {
  const [primes, setPrimes] = useState<Prime[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPrime, setEditingPrime] = useState<Prime | null>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const fetchPrimes = async () => {
    setLoading(true);
    try {
      const response = await getPrimes();
      setPrimes(response.data);
      setTotal(response.total);
    } catch (error) {
      message.error("Erreur lors du chargement des primes");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrimes();
  }, []);

  const handleBack = () => {
    navigate("/admin/primes");
  };

  const handleAdd = () => {
    setEditingPrime(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (prime: Prime) => {
    setEditingPrime(prime);
    form.setFieldsValue({
      ...prime,
      date: dayjs(prime.date)
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: "Êtes-vous sûr de vouloir supprimer cette prime ?",
      content: "Cette action est irréversible.",
      okText: "Oui",
      okType: "danger",
      cancelText: "Non",
      onOk: async () => {
        try {
          await deletePrime(id);
          message.success("Prime supprimée avec succès");
          fetchPrimes();
        } catch (error) {
          message.error("Erreur lors de la suppression de la prime");
        }
      },
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const formData: PrimeFormData = {
        ...values,
        date: values.date.toISOString(),
      };

      if (editingPrime) {
        await updatePrime(editingPrime.id, formData);
        message.success("Prime modifiée avec succès");
      } else {
        await createPrime(formData);
        message.success("Prime créée avec succès");
      }

      setIsModalVisible(false);
      fetchPrimes();
    } catch (error) {
      message.error("Erreur lors de l'enregistrement de la prime");
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const getPrimeTypeLabel = (type: PrimeType) => {
    const labels = {
      [PrimeType.ANNUAL]: "Annuelle",
      [PrimeType.PERFORMANCE]: "Performance",
      [PrimeType.PROJECT]: "Projet",
      [PrimeType.EXCEPTIONAL]: "Exceptionnelle",
      [PrimeType.OTHER]: "Autre",
    };
    return labels[type] || type;
  };

  const getPrimeStatusColor = (status: PrimeStatus) => {
    const colors = {
      [PrimeStatus.DRAFT]: "default",
      [PrimeStatus.PENDING]: "processing",
      [PrimeStatus.APPROVED]: "success",
      [PrimeStatus.REJECTED]: "error",
    };
    return colors[status] || "default";
  };

  const getPrimeStatusLabel = (status: PrimeStatus) => {
    const labels = {
      [PrimeStatus.DRAFT]: "Brouillon",
      [PrimeStatus.PENDING]: "En attente",
      [PrimeStatus.APPROVED]: "Approuvée",
      [PrimeStatus.REJECTED]: "Rejetée",
    };
    return labels[status] || status;
  };

  const columns = [
    {
      title: "Nom",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      render: (type: PrimeType) => getPrimeTypeLabel(type),
    },
    {
      title: "Montant",
      dataIndex: "amount",
      key: "amount",
      render: (amount: number) => `${amount.toLocaleString()} €`,
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: "Statut",
      dataIndex: "status",
      key: "status",
      render: (status: PrimeStatus) => (
        <Tag color={getPrimeStatusColor(status)}>
          {getPrimeStatusLabel(status)}
        </Tag>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: Prime) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Modifier
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            Supprimer
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: (
          <Space>
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
            />
            <Typography.Title level={3} style={{ margin: 0 }}>
              Gestion des Primes
            </Typography.Title>
          </Space>
        ),
        extra: (
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            Ajouter une prime
          </Button>
        )
      }}
    >
      <Table
        dataSource={primes}
        columns={columns}
        rowKey="id"
        loading={loading}
        pagination={{
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} primes`,
        }}
      />

      <Modal
        title={editingPrime ? "Modifier la prime" : "Ajouter une prime"}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
        okText="Enregistrer"
        cancelText="Annuler"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: PrimeStatus.DRAFT,
            avec_unite: false,
          }}
        >
          <Form.Item
            name="name"
            label="Nom de la prime"
            rules={[{ required: true, message: "Veuillez saisir le nom de la prime" }]}
          >
            <Input placeholder="Nom de la prime" />
          </Form.Item>

          <Form.Item
            name="type"
            label="Type de prime"
            rules={[{ required: true, message: "Veuillez sélectionner le type de prime" }]}
          >
            <Select placeholder="Sélectionner le type">
              <Option value={PrimeType.ANNUAL}>Annuelle</Option>
              <Option value={PrimeType.PERFORMANCE}>Performance</Option>
              <Option value={PrimeType.PROJECT}>Projet</Option>
              <Option value={PrimeType.EXCEPTIONAL}>Exceptionnelle</Option>
              <Option value={PrimeType.OTHER}>Autre</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="amount"
            label="Montant (€)"
            rules={[{ required: true, message: "Veuillez saisir le montant" }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
              placeholder="Montant en euros"
            />
          </Form.Item>

          <Form.Item
            name="date"
            label="Date"
            rules={[{ required: true, message: "Veuillez sélectionner la date" }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="status"
            label="Statut"
            rules={[{ required: true, message: "Veuillez sélectionner le statut" }]}
          >
            <Select placeholder="Sélectionner le statut">
              <Option value={PrimeStatus.DRAFT}>Brouillon</Option>
              <Option value={PrimeStatus.PENDING}>En attente</Option>
              <Option value={PrimeStatus.APPROVED}>Approuvée</Option>
              <Option value={PrimeStatus.REJECTED}>Rejetée</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="periodicite"
            label="Périodicité"
          >
            <Select placeholder="Sélectionner la périodicité" allowClear>
              <Option value={Periodicite.ANNUELLE}>Annuelle</Option>
              <Option value={Periodicite.TRIMESTRIELLE}>Trimestrielle</Option>
              <Option value={Periodicite.MENSUELLE}>Mensuelle</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="avec_unite"
            label="Avec unité de versement"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={3} placeholder="Description de la prime" />
          </Form.Item>

          <Form.Item
            name="periode"
            label="Période"
          >
            <Input placeholder="Période (ex: 2023, T1, Janvier)" />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default PrimeManagementPage;
