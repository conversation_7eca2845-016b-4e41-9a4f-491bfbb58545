import React, { StrictMode } from "react"
import { createRoot } from "react-dom/client"

import "./index.css"
import "./locales/i18n"

import App from "./App"

if (import.meta.env.DEV && import.meta.env.VITE_USE_MIRAGE) {
  import('./mocks').then(({ startMirageServer }) => startMirageServer());
  console.info("=================== Mirage server started ===================");
}

document.addEventListener('DOMContentLoaded', ()=> {
  const rootElement = document.getElementById('root');
  if (rootElement) {
    createRoot(rootElement).render(
      React.createElement(StrictMode, null,
        React.createElement(App, null)
      )
    )
  }
})

