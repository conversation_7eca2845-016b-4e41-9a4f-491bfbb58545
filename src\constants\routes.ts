function path(sublink: string, root: string = "/") {
  sublink = sublink.startsWith("/") ? sublink.substring(1) : sublink;
  root = root.endsWith("/") ? root : `/${root}`;
  return root === "/" ? `/${sublink}` : `${root}/${sublink}`
}

export const ROOTS_HOME = "/home"
export const ROOTS_AUTH = "/auth"
export const ROOTS_ADMIN = "/admin"

export const HOME = "home"
export const AUTH = "auth"
export const ADMIN = "admin"
export const SIGNIN = "signin"
export const REQUEST = "request"
export const UPDATE_PASSWORD = "update-password"
export const SERVICES = "services"
export const LOGIN = "login"
export const DASHBOARD = "dashboard"
export const AGENTS = "agents"
export const CONVOYAGE = "convoyage"
export const UTILISATEURS = "utilisateurs"
export const PRIMES = "primes"

export const PATH_GUEST = {
  root: path(HOME),
  login: path(LOGIN),
  services: path(SERVICES),
}

export const PATH_AUTH = {
  root: path(AUTH),
  signin: path(SIGNIN, AUTH),
  request: path(REQUEST, AUTH),
  updatePassword: path(UPDATE_PASSWORD, AUTH),
}

export const PATH_ADMIN = {
  root: path(ADMIN),
  admin: path(DASHBOARD, ADMIN),
  agents: path(AGENTS, ADMIN),
  convoyage: path(CONVOYAGE, ADMIN),
  utilisateurs: path(UTILISATEURS, ADMIN),
  primes: path(PRIMES, ADMIN),
  primesManagement: path(`${PRIMES}/management`, ADMIN),
  primesCoefficients: path(`${PRIMES}/coefficients`, ADMIN),
}

export const PATH_LINKS = {
  customsWebb: "https://cw.douanes.bj/",
  guce: "https://guce.gouv.bj/",
  gouv: "https://www.gouv.bj/",
  finance: "https://finances.bj/",
  omd: "http://www.wcoomd.org/fr.aspx",
  omc: "https://www.wto.org/",
  ecowas: "https://www.ecowas.int/?lang=fr",
  bc: "https://www.benincontrol.com/",
  map: "https://goo.gl/maps/bCPg6bWbZ1KMWQoc8",
}

export const PATH_ERROR = {
  error403: "/error/403",
  error404: "/error/404",
  error500: "/error/500",
}

export const PATH_GITHUB = {
  repo: "https://github.com/votre-organisation/erp-douanes",
}

export const DASHBOARD_ITEMS = [
  {
    title: "Tableau de bord principal",
    path: PATH_ADMIN.admin,
  },
  {
    title: "Gestion des agents",
    path: PATH_ADMIN.agents,
  },
  {
    title: "Convoyage",
    path: PATH_ADMIN.convoyage,
  },
  {
    title: "Primes",
    path: PATH_ADMIN.primes,
  },
]
