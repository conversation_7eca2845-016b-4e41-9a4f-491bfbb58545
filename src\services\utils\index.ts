import IdentityManager from "../IdentityManager"

export { themeIcon } from "./themeIcon"
export { loadAllMenus } from "./loadAllMenus"

export const waitTime = (time: number = 100): Promise<boolean> => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

export function getBase64(file: Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

export function getBase64WithCallback(img: Blob, callback: { (url: any): void; (arg0: string | ArrayBuffer | null): any }) {
  const reader = new FileReader()
  reader.addEventListener("load", () => callback(reader.result))
  reader.readAsDataURL(img)
}

export const toggleTheme = (theme: any): any => {
  switch (theme) {
    case "auto":
      return "dark"
    case "dark":
      return "light"
    default:
      return "auto"
  }
}

export const device = /(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent) ? "MOBILE" : "DESKTOP"

export const toPath = (str: string): string => (!str ? import.meta.env.BASE_URL : `${str.startsWith("/") ? '':'/'}${str}${str.endsWith("/") ? '':'/'}`)


export const getUUID = (name?: string, nbrVersion: number = 4): string => {
  return !name ? IdentityManager.fetchUUID(nbrVersion) :  name + `_${IdentityManager.fetchUUID(nbrVersion)}`
}
