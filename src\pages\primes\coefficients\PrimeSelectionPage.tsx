import { useEffect, useState } from "react";
import { PageContainer } from "@ant-design/pro-components";
import { 
  Button, 
  Typography, 
  Card, 
  Row, 
  Col, 
  Space, 
  Tag, 
  message,
  Spin
} from "antd";
import { ArrowLeftOutlined, ArrowRightOutlined } from "@ant-design/icons";
import { Prime, PrimeStatus, PrimeType } from "../../../models/Prime";
import { getPrimes } from "../../../services/api/primeService";
import { useNavigate } from "react-router";

const PrimeSelectionPage = () => {
  const [primes, setPrimes] = useState<Prime[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const fetchPrimes = async () => {
    setLoading(true);
    try {
      const response = await getPrimes();
      // Filtrer seulement les primes approuvées pour la gestion des coefficients
      const approvedPrimes = response.data.filter(prime => prime.status === PrimeStatus.APPROVED);
      setPrimes(approvedPrimes);
    } catch (error) {
      message.error("Erreur lors du chargement des primes");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrimes();
  }, []);

  const handleBack = () => {
    navigate("/admin/primes");
  };

  const handleSelectPrime = (primeId: string) => {
    navigate(`/admin/primes/coefficients/${primeId}`);
  };

  const getPrimeTypeLabel = (type: PrimeType) => {
    const labels = {
      [PrimeType.ANNUAL]: "Annuelle",
      [PrimeType.PERFORMANCE]: "Performance",
      [PrimeType.PROJECT]: "Projet",
      [PrimeType.EXCEPTIONAL]: "Exceptionnelle",
      [PrimeType.OTHER]: "Autre",
    };
    return labels[type] || type;
  };

  const getPrimeTypeColor = (type: PrimeType) => {
    const colors = {
      [PrimeType.ANNUAL]: "blue",
      [PrimeType.PERFORMANCE]: "green",
      [PrimeType.PROJECT]: "orange",
      [PrimeType.EXCEPTIONAL]: "purple",
      [PrimeType.OTHER]: "default",
    };
    return colors[type] || "default";
  };

  return (
    <PageContainer
      header={{
        title: (
          <Space>
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
            />
            <Typography.Title level={3} style={{ margin: 0 }}>
              Sélection de Prime pour Gestion des Coefficients
            </Typography.Title>
          </Space>
        ),
      }}
    >
      <div style={{ marginBottom: 24 }}>
        <Typography.Paragraph>
          Sélectionnez une prime pour gérer les coefficients par fonction. 
          Seules les primes approuvées sont disponibles pour la gestion des coefficients.
        </Typography.Paragraph>
      </div>

      <Spin spinning={loading}>
        {primes.length === 0 && !loading ? (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Typography.Title level={4} type="secondary">
                Aucune prime approuvée disponible
              </Typography.Title>
              <Typography.Paragraph type="secondary">
                Veuillez d'abord créer et approuver des primes dans la section "Gestion des Primes".
              </Typography.Paragraph>
              <Button 
                type="primary" 
                onClick={() => navigate("/admin/primes/management")}
              >
                Aller à la gestion des primes
              </Button>
            </div>
          </Card>
        ) : (
          <Row gutter={[16, 16]}>
            {primes.map((prime) => (
              <Col xs={24} sm={12} md={8} lg={6} key={prime.id}>
                <Card
                  hoverable
                  style={{ height: '100%' }}
                  actions={[
                    <Button
                      type="primary"
                      icon={<ArrowRightOutlined />}
                      onClick={() => handleSelectPrime(prime.id)}
                      block
                    >
                      Gérer les coefficients
                    </Button>
                  ]}
                >
                  <Card.Meta
                    title={
                      <div>
                        <Typography.Text strong ellipsis>
                          {prime.name}
                        </Typography.Text>
                        <div style={{ marginTop: 8 }}>
                          <Tag color={getPrimeTypeColor(prime.type)}>
                            {getPrimeTypeLabel(prime.type)}
                          </Tag>
                        </div>
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: 8 }}>
                          <Typography.Text strong>
                            {prime.amount.toLocaleString()} €
                          </Typography.Text>
                        </div>
                        <div style={{ marginBottom: 8 }}>
                          <Typography.Text type="secondary">
                            {new Date(prime.date).toLocaleDateString()}
                          </Typography.Text>
                        </div>
                        {prime.description && (
                          <Typography.Paragraph 
                            ellipsis={{ rows: 2 }}
                            style={{ margin: 0, fontSize: '12px' }}
                          >
                            {prime.description}
                          </Typography.Paragraph>
                        )}
                        {prime.periode && (
                          <div style={{ marginTop: 8 }}>
                            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                              Période: {prime.periode}
                            </Typography.Text>
                          </div>
                        )}
                      </div>
                    }
                  />
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </Spin>
    </PageContainer>
  );
};

export default PrimeSelectionPage;
