import { EditableProTable } from "@ant-design/pro-components"
import { Form, Tabs } from "antd"
import { waitTime } from "src/services/utils"
import { useRef, useState } from "react"

const columnsEnfants = [
  {
    key: "prenom",
    title: "Prenoms",
    dataIndex: "prenom",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "dateNais",
    title: "Date naissance",
    dataIndex: "dateNais",
    valueType: "date",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "lieuNais",
    title: "Lieu naissance",
    dataIndex: "lieuNais",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "acteNais",
    title: "Acte naissance",
    dataIndex: "acteNais",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "sexe",
    title: "Sexe",
    dataIndex: "sexe",
    valueType: "select",
    valueEnum: {
      M: { text: "Masculin" },
      F: { text: "Feminin" },
    },
  },
  {
    key: "charge",
    title: "A charge",
    dataIndex: "charge",
    width: "20%",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    // renderFormItem: (_, { isEditable }) => {
    //   return <Input />
    // },
    // render: (_, row) => row?.labels?.map(item => <Tag key={item.key}>{item.label}</Tag>),
  },
  {
    key: "dateDeces",
    title: "Date decès enfant",
    dataIndex: "dateDeces",
    valueType: "date",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    title: "options",
    valueType: "option",
    width: 250,
    render: (text, record, _, action) => [
      <a
        key="editable"
        onClick={() => {
          action?.startEditable?.(record.id)
        }}
      >
        edit
      </a>,
      <a
        key="delete"
        onClick={() => {
          // const tableDataSource = formRef.current?.getFieldValue(
          //   'table',
          // )
          // const tableDataSource = defaultData
          // formRef.current?.setFieldsValue({
          //   table: tableDataSource.filter(item => item.id !== record?.id),
          // })
          console.log(text)
        }}
      >
        delete
      </a>,
      <EditableProTable.RecordCreator
        key="copy"
        record={{
          ...record,
          id: (Math.random() * 1000000).toFixed(0),
        }}
      >
        <a>copy</a>
      </EditableProTable.RecordCreator>,
    ],
  },
]

const columnsConjoints = [
  {
    key: "nom",
    title: "Nom",
    dataIndex: "nom",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "prenom",
    title: "Prenom",
    dataIndex: "prenom",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "dateNais",
    title: "Date naissance",
    dataIndex: "dateNais",
    valueType: "date",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "liaison",
    title: "Type liaison",
    dataIndex: "liaison",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "refActeMariage",
    title: "Ref. acte mariage",
    dataIndex: "refActeMariage",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "dateDeces",
    title: "Date décès",
    dataIndex: "dateDeces",
    valueType: "date",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "matricule",
    title: "Matricule",
    dataIndex: "matricule",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "required",
        },
      ],
    },
    width: "30%",
  },
  {
    key: "options",
    title: "options",
    valueType: "option",
    width: 250,
    render: (text, record, _, action) => [
      <a
        key="editable"
        onClick={() => {
          action?.startEditable?.(record.id)
        }}
      >
        edit
      </a>,
      <a
        key="delete"
        onClick={() => {
          // const tableDataSource = formRef.current?.getFieldValue(
          //   'table',
          // )
          // const tableDataSource = defaultData
          // formRef.current?.setFieldsValue({
          //   table: tableDataSource.filter(item => item.id !== record?.id),
          // })
          console.log(text)
        }}
      >
        delete
      </a>,
      <EditableProTable.RecordCreator
        key="copy"
        record={{
          ...record,
          id: (Math.random() * 1000000).toFixed(0),
        }}
      >
        <a>copy</a>
      </EditableProTable.RecordCreator>,
    ],
  },
]

function SituationMatrimoniale(params) {
  const actionRefEnfant = useRef()
  const actionRefConjoint = useRef()
  const [editableKeysEnfant, setEditableKeysEnfant] = useState([])
  const [editableKeysConjoint, setEditableKeysConjoint] = useState([])
  const [formEnfant] = Form.useForm()
  const [formConjoint] = Form.useForm()
  const [dataSource, setDataSource] = useState([])

  const items = [
    {
      key: "1",
      label: "Enfants",
      children: (
        <EditableProTable
          rowKey="id"
          scroll={{
            x: 760,
          }}
          actionRef={actionRefEnfant}
          headerTitle="Table enfants"
          maxLength={5}
          recordCreatorProps={{
            record: index => {
              return { id: index + 1 }
            },
          }}
          controlled
          columns={columnsEnfants}
          name={"situationMatriEnfants"}
          editable={{
            form: formEnfant,
            editableKeys: editableKeysEnfant,
            onSave: async () => {
              await waitTime(2000)
            },
            onChange: setEditableKeysEnfant,
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
        />
      ),
    },
    {
      key: "2",
      label: "Conjoints",
      children: (
        <EditableProTable
          rowKey="id"
          scroll={{
            x: 760,
          }}
          actionRef={actionRefConjoint}
          headerTitle="Table conjoints"
          maxLength={5}
          recordCreatorProps={{
            record: index => {
              return { id: index + 1 }
            },
          }}
          controlled
          columns={columnsConjoints}
          name={"situationMatriConjoints"}
          editable={{
            form: formConjoint,
            editableKeys: editableKeysConjoint,
            onSave: async () => {
              await waitTime(2000)
            },
            onChange: setEditableKeysConjoint,
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
        />
      ),
    },
  ]
  return (
    // <>
    <Tabs defaultActiveKey="1" type="card" size="small" items={items} />
    // </>
  )
}

export default SituationMatrimoniale
