import { EyeOutlined, SaveOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON><PERSON>r,
  ProCard,
  ProForm,
  ProFormDatePicker,
  ProFormGroup,
  ProFormList,
  ProFormText
} from "@ant-design/pro-components";
import { Button, Card, message, Typography } from "antd";
import { useState } from "react";
import { useActionData, useLoaderData, useSubmit } from "react-router";
import request from '~/services/request';
import { waitTime } from "~/services/utils";
import { createStyles, cx } from "~/themes";
import { actionConvoyagePage, loaderConvoyagePage } from "./_ConvoyagePage";

const { Title, Text, Paragraph } = Typography

  interface ProduitChargeItem {
    produitCharge: string;
    poidsProduct: string; // ou `number` selon le type attendu avant transformation
    nombreSac50: string; // ou `number`
    nombreSac100: string; // ou `number`
  }

  type TransportFiche = {
    serialNumber: string;
    statut: string;
  };

interface ConvoyageFormValues {
  cuoCode: string;
  numCamion: string;
  nomConducteur: string;
  prenomConducteur: string;
  numPermisConducteur: string;
  produitCharge: ProduitChargeItem[];
  contact: string;
  nameOwner: string;
  firstnameOwner: string;
  contactOwner: string;
  communeProduct: string;
  village: string;
  destinationProduct: string;
  date: string;
}

function ConvoyagePage() {
  const { styles } = useStyles()
  const loaderData = useLoaderData<typeof loaderConvoyagePage>()
  const actionData = useActionData<typeof actionConvoyagePage>()
  const [res, setRes] = useState<TransportFiche | null>(null);

  const handleSubmit = async (values: ConvoyageFormValues) => {
    console.log("Submitting values:", values);

    // Restructuration des données
    const produitCharge = values.produitCharge.map((item) => item.produitCharge);
    const poidsProduct = values.produitCharge.map((item) => parseFloat(item.poidsProduct));
    const nombreSac50 = values.produitCharge.map((item) => parseInt(item.nombreSac50, 10));
    const nombreSac100 = values.produitCharge.map((item) => parseInt(item.nombreSac100, 10));

    const formattedValues = {
      ...values,
      produitCharge,
      poidsProduct,
      nombreSac50,
      nombreSac100,
    };

    console.log("Formatted values:", formattedValues);

    try {
      const response = await request.post('/transport/create', {
        data: formattedValues,
      });
      message.success('Enregistré avec succès');
      setRes(response); // Enregistrer la réponse si nécessaire
    } catch (error) {
      message.error('Erreur lors de l\'enregistrement');
      console.error(error);
    }
  };

  const handleVerify = async (values: any) => {
    try {
      const response = await request.get(`/transport/find/${values.serialNumber}`);
      message.success("Recherche réussie");
      setRes(response.data);
    } catch (error) {
      message.error("Erreur lors de la recherche!!!! Veuillez vérifier le numéro de série.");
      console.error(error);
    }
  };

  return (
    <PageContainer
      content={
        <Typography.Title level={3} className="text-center">
          PLATEFORME DE RENSEIGNEMENTS DE CONVOYAGE DES PRODUITS AGRICOLES
        </Typography.Title>
      }
      tabList={[
        {
          key: "base",
          tab: "SOUMETTRE",
          children: <ConvoyageForm res={res} onSubmit={handleSubmit} />,
        },
        {
          key: "info",
          tab: "VERIFIER",
          children: (
            <ProCard>
              <ProForm
                layout={"vertical"}
                grid
                rowProps={{
                  gutter: [16, 0],
                }}
                submitter={{
                  render: ({ onSubmit }, doms) => {
                    return (
                      <ProCard layout="center">
                        <Button
                          type="primary"
                          htmlType="submit"
                          icon={<EyeOutlined />}
                          onClick={() => onSubmit?.()}
                        >
                          Vérifier
                        </Button>
                      </ProCard>
                    );
                  },
                }}
                onFinish={async (values) => {
                  await waitTime(2000);
                  handleVerify(values); // Appel API pour vérifier la fiche
                }}
              >
                <ProCard layout="center" className={styles.clearPadding}>
                  <ProFormText
                    colProps={{ sm: 30, lg: 30 }}
                    label="N° D'identification"
                    name="serialNumber"
                    rules={[{ required: true, message: "Information manquante" }]}
                  />
                </ProCard>
              </ProForm>

              {/* Résultats de la recherche */}
              {res && (
                <ProCard>
                  <Typography.Title level={3} className="text-center">
                    Résultats de la Recherche
                  </Typography.Title>
                 <Typography.Paragraph
                   style={{
                     fontSize: "16px",
                     lineHeight: "1.6",
                     marginBottom: "12px",
                     marginLeft:"50px",
                     fontFamily: "Verdana, sans-serif"
                   }}
                 >
                   <b>Numéro de série :</b>{" "}
                     <Typography.Text
                       style={{
                         fontWeight: "bold",
                         color: "#1890ff",
                         fontSize: "18px",
                       }}
                     >
                       {res.serialNumber}
                     </Typography.Text>
                 </Typography.Paragraph>
                   <Typography.Paragraph
                      style={{
                        fontSize: "16px",
                        lineHeight: "1.6",
                        marginBottom: "12px",
                        marginLeft:"50px",
                        fontFamily: "Verdana, sans-serif"

                      }}>
                    <b>Statut:</b>{" "}
                      <Typography.Text
                        style={{
                          fontWeight: "bold",
                          color: "#1890ff",
                          fontSize: "18px",
                        }}
                      >
                        {res.statut}
                      </Typography.Text>
                  </Typography.Paragraph>
                </ProCard>
              )}
            </ProCard>
          ),
        },
      ]}
      tabProps={{
        type: "card",
        centered: true,
        animated: true,
        className: cx(styles.headerNav),
        onChange: (key) => {
          if (key === "base") setRes(null); // Réinitialisez les résultats lorsque vous revenez à l'onglet Soumettre
        },
      }}
    />
  );

}
export default ConvoyagePage

function ConvoyageForm({ res, onSubmit }: any) {
  const { styles } = useStyles()
  const submit = useSubmit()

  if (res)
    return (
      <ProCard layout="center">
        <Card bordered className="inline-block p-5 shadow-md text-center">
          <Title level={4} style={{ marginBottom: "2rem" }}>
            FELICITATION
          </Title>
          <Paragraph>
            Votre fiche a bien été enregistrée sous le numéro: <b>{res.serialNumber}</b>
          </Paragraph>
          {/* <Paragraph>
            <a href="#file">Cliquer ici</a> pour télécharger votre fiche de convoyage
          </Paragraph> */}
          <Button
            type="primary"
            size="large"
            onClick={() => {
              return;
            }}
          >
            Revenir au formulaire
          </Button>
        </Card>
      </ProCard>
    );

  return (
    <ProCard>
      <ProForm
        layout={"vertical"}
        grid
        rowProps={{
          gutter: [16, 0],
        }}
        submitter={{
          render: ({ onSubmit: formSubmit }, doms) => {
            return (
              <ProCard layout="center">
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />} onClick={() => formSubmit?.()}>
                  Enregistrer
                </Button>
              </ProCard>
            )
          },
        }}
        onFinish={async values => {
          await waitTime(2000);
          onSubmit(values); // Appeler onSubmit avec les valeurs du formulaire
        }}
      >
        <ProCard layout="center" className={styles.clearPadding}>
          <ProFormText
            colProps={{ sm: 8, lg: 6 }}
            label="Bureau de Douane"
            name="cuoCode"
            rules={[{ required: true, message: "Information manquante" }]}
          />
          <ProFormText
            colProps={{ sm: 12, lg: 6 }}
            label="N° Camion"
            name="numCamion"
            rules={[{ required: true, message: "Information manquante" }]}
          />
        </ProCard>
        <ProFormText
          colProps={{ sm: 12, lg: 6 }}
          label="Nom du chauffeur"
          name="nomConducteur"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormText
          colProps={{ sm: 12, lg: 6 }}
          label="Prenom du chauffeur"
          name="prenomConducteur"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormText
          colProps={{ sm: 12, lg: 6 }}
          label="Contact chauffeur"
          name="contact"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormText
          colProps={{ sm: 12, lg: 6 }}
          label="N° permis de conduire"
          name="numPermisConducteur"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormText
          colProps={{ sm: 12, lg: 8 }}
          label="Nom du propriétaire"
          name="nameOwner"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormText
          colProps={{ sm: 12, lg: 8 }}
          label="Prenom du propriétaire"
          name="firstnameOwner"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormText
          colProps={{ sm: 12, lg: 8 }}
          label="Contact du propriétaire"
          name="contactOwner"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormList
          name={"produitCharge"}
          creatorButtonProps={{
            creatorButtonText: "Ajouter un produit",
          }}
          copyIconProps={{
            tooltipText: "Recopier la ligne",
          }}
          deleteIconProps={{
            tooltipText: "Supprimer la ligne",
          }}
        >
          <ProFormGroup>
            <ProFormText
              colProps={{ sm: 24, lg: 6 }}
              label="Produit Chargé"
              name="produitCharge"
              rules={[{ required: true, message: "Information manquante" }]}
            />
            <ProFormText
              colProps={{ sm: 12, lg: 6 }}
              label="Poids (kg)"
              name="poidsProduct"
              rules={[{ required: true, message: "Information manquante" }]}
            />
            <ProFormText
              colProps={{ sm: 12, lg: 6 }}
              label="Nombre de sacs (50 kg)"
              name="nombreSac50"
              rules={[{ required: true, message: "Information manquante" }]}
            />
            <ProFormText
              colProps={{ sm: 12, lg: 6 }}
              label="Nombre de sacs (100 kg)"
              name="nombreSac100"
              rules={[{ required: true, message: "Information manquante" }]}
            />
          </ProFormGroup>
        </ProFormList>

        <ProFormText
          colProps={{ sm: 12, lg: 8 }}
          label="Commune de production"
          name="communeProduct"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormText
          colProps={{ sm: 12, lg: 8 }}
          label="Village"
          name="village"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormText
          colProps={{ sm: 12, lg: 8 }}
          label="Destination"
          name="destinationProduct"
          rules={[{ required: true, message: "Information manquante" }]}
        />
        <ProFormDatePicker
          colProps={{ sm: 12, lg: 8 }}
          label="Date"
          name="date"
          rules={[{ required: true, message: "Information manquante" }]}
        />
      </ProForm>
    </ProCard>
  )
}

const useStyles = createStyles(({ css, prefixCls }) => ({
  headerNav: css`
    & > .${prefixCls}-tabs-nav {
      margin-bottom: 0;
    }
  `,
  clearPadding: css`
    & > * {
      padding-block: 0 !important;
    }
  `,
}))
