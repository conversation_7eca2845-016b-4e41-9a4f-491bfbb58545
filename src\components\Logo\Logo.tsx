import { Flex, type FlexProps, Image, theme, Typography } from "antd"
// import { Link } from "react-router"
import { type CSSProperties } from "react"
import { LogoDouane } from "../LogoDouane.svg"
import { css, cx } from "~/themes"
import { useNavigate } from "react-router"

const {Link} = Typography

type LogoProps = {
  color: CSSProperties["color"]
  src?: string
  alt?: string
  imgSize?: {
    h?: number | string
    w?: number | string
  }
  asLink?: boolean
  href?: string
  bgColor?: CSSProperties["backgroundColor"]
  noTexte?: boolean
} & Partial<FlexProps>

export const Logo = ({ asLink, noTexte, color, href, imgSize, bgColor, src, alt, ...others }: LogoProps) => {
  const navigate = useNavigate()
  const {
    token: { borderRadius },
  } = theme.useToken()

  noTexte ??= false

  return asLink ? (
    // <Link href={href ?? "#"} className={style}>
      <Flex gap={others.gap ?? "small"} align="center" {...others} onClick={() => navigate(href ?? "/")}>
        {src ? (
          <Image preview={false} src={src} alt={alt} style={{ height: imgSize?.h ?? 48 }} />
        ) : (
          <LogoDouane height={imgSize?.h ?? 48} width={imgSize?.w ?? 48} />
        )}
        {noTexte ? null : (
          <Typography.Title
            level={5}
            type="secondary"
            style={{
              color,
              margin: 0,
              padding: `4px 8px`,
              backgroundColor: bgColor,
              borderRadius,
            }}
          >
            Douane Béninoise
          </Typography.Title>
        )}
      </Flex>
    // </Link>
  ) : (
    <Flex gap={others.gap ?? "small"} align="center" {...others}>
      {src ? (
        <Image preview={false} src={src} alt={alt} style={{ height: imgSize?.h ?? 48 }} />
      ) : (
        <LogoDouane height={imgSize?.h ?? 48} width={imgSize?.w ?? 48} />
      )}
      {noTexte ? null : (
        <Typography.Title
          level={5}
          type="secondary"
          style={{
            color,
            margin: 0,
            padding: `4px 8px`,
            backgroundColor: bgColor,
            borderRadius,
          }}
        >
          Douane Béninoise
        </Typography.Title>
      )}
    </Flex>
  )
}

const style = cx(css`
  .logo-link {
    text-decoration: none;
  }
`)
