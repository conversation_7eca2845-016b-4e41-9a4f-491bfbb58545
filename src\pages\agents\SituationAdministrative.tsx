import {
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormGroup,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from "@ant-design/pro-components"

function SituationAdministrative() {
  return (
    <ProFormGroup
      style={{
        gap: "0 32px",
      }}
    >
      <ProFormText
        // width="md"
        name={["matricule"]}
        label="Matricule"
        colProps={{
          span: 8,
        }}
      />
      <ProFormText
        // width="md"
        name={["situationAd", "codeCollect"]}
        label="Code collectivité"
        colProps={{
          span: 8,
        }}
      />
      <ProFormCheckbox
        name={["situationAd", "nonActive"]}
        label="Non Active"
        colProps={{
          span: 8,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "En service", value: "service" },
          { label: "En congé", value: "congé" },
        ]}
        name={["situationAd", "positionActuel"]}
        label="Position actuelle"
        colProps={{
          span: 12,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "Status A", value: "A" },
          { label: "Status B", value: "B" },
          { label: "Status C", value: "C" },
        ]}
        name={["situationAd", "statusAgent"]}
        label="Status agent"
        colProps={{
          span: 12,
        }}
      />
      {/* <ProFormText width="md" name="id" label="ID AGENT" /> */}
      <ProFormDatePicker
        // width="sm"
        name={["situationAd", "datePriseServFp"]}
        label="Date Prise Serv FP"
        colProps={{
          span: 8,
        }}
      />
      <ProFormDatePicker
        // width="sm"
        name={["situationAd", "datePriseServAdm"]}
        label="Date Prise Serv Adm"
        colProps={{
          span: 8,
        }}
      />
      <ProFormDatePicker
        // width="sm"
        name={["situationAd", "dateNomEmploi"]}
        label="Date nomination à l'emploi"
        colProps={{
          span: 8,
        }}
      />
      <ProFormText
        // width="md"
        name={["situationAd", "tel"]}
        label="Téléphone"
        colProps={{
          span: 12,
        }}
      />
      <ProFormText
        // width="md"
        name={["situationAd", "inter"]}
        label="Interphone"
        colProps={{
          span: 12,
        }}
      />
      <ProFormText
        // width="md"
        name={["situationAd", "structure"]}
        label="Structure"
        colProps={{
          span: 8,
        }}
      />
      <ProFormText
        // width="xs"
        name={["situationAd", "sousStructure"]}
        label="Sous structure"
        colProps={{
          span: 8,
        }}
      />
      <ProFormText
        // width="md"
        name={["situationAd", "servBurAss"]}
        label="Serv. Bur. ou Ass."
        colProps={{
          span: 8,
        }}
      />
      <ProFormRadio.Group
        name={["situationAd", "regimePension"]}
        label="pension"
        options={[
          {
            label: "FNRB",
            value: "fnrb",
          },
          {
            label: "CNSS",
            value: "cnss",
          },
          {
            label: "AUTRE",
            value: "autre",
          },
        ]}
        colProps={{
          span: 12,
        }}
      />
      <ProFormText
        // width="md"
        name={["situationAd", "profAvantEntrerService"]}
        label="Profession avant l'entrer dans le service"
        colProps={{
          span: 12,
        }}
      />
      <ProFormSelect
        // width="md"
        // fieldProps={{
        //   labelInValue: true,
        // }}
        request={async () => [
          { label: "A", value: "a" },
          { label: "B", value: "b" },
        ]}
        name={["situationAd", "caisse"]}
        label="Caisse"
        colProps={{
          span: 12,
        }}
      />
    </ProFormGroup>
  )
}

export default SituationAdministrative
