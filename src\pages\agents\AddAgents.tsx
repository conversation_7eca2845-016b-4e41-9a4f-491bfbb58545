import { PlusOutlined } from "@ant-design/icons"
import { StepsForm } from "@ant-design/pro-components"
import { Button, message, Modal, Tabs } from "antd"
import { waitTime } from "src/services/utils"
// import accounts from "assets/imgs/account.svg"
import { useRef, useState } from "react"
import Draggable from "react-draggable"
import Enfants from "./Enfants"
import EtatCivil from "./EtatCivil"
import SituationAdministrative from "./SituationAdministrative"
import SituationFinanciere from "./SituationFinanciere"

function AddAgents() {
  const restFormRef = useRef()
  const draggleRef = useRef(null)
  const modalRef = useRef(null)
  const [disabled, setDisabled] = useState(true)
  const [open, setOpen] = useState(false)
  // const [loading, setLoading] = useState(true)
  const [current, setCurrent] = useState(0)

  const [resultForm, setResultForm] = useState({})
  const [imageUrl, setImageUrl] = useState()

  const showModal = e => {
    setOpen(true)
    // setLoading(true);
    // Simple loading mock. You should add cleanup logic in real world.
    setTimeout(() => {
      // setLoading(false);
    }, 500)
  }
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  })

  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement
    const targetRect = draggleRef.current?.getBoundingClientRect()
    if (!targetRect) {
      return
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    })
  }

  const handleOk = e => {
    // console.log("OK ",e);
    setOpen(false)
  }
  const handleCancel = e => {
    // console.log("Cancel ",e);
    setOpen(false)
  }

  const tabItems = [
    {
      key: "1",
      label: "Etat civile",
      children: <EtatCivil imageUrl={imageUrl} onImageChange={url => setImageUrl(url)} />,
    },
    {
      key: "2",
      label: "Situation Administrative",
      children: <SituationAdministrative />,
    },
    {
      key: "3",
      label: "Situation Financière",
      children: <SituationFinanciere />,
    },
  ]

  return (
    <div>
      <Button key="create" type="link" onClick={() => showModal()}>
        <PlusOutlined />
      </Button>
      <StepsForm
        // stepsProps={{
        //   direction: 'vertical',
        // }}
        // formRef={restFormRef}
        current={current}
        onFinish={async data => {
          // await waitTime(1000)
          data.profil = imageUrl
          console.log("data :", data)
          // const newdata = { ...data, categorie: data.categorie.value }
          // console.log("newdata :", newdata)
          // console.log(JSON.stringify(data))

          // const value = await apiCreateAgent(data)
          // console.log("value ", value)
          message.success("Valider")
          setOpen(false)
          setCurrent(0)
        }}
        formProps={{
          validateMessages: {
            required: "Champs obligatoire",
          },
        }}
        stepsFormRender={(dom, submitter) => {
          return (
            <Modal
              title={
                <div
                  style={{
                    width: "100%",
                    cursor: "move",
                    textAlign: "center",
                    marginBottom: "1.5rem",
                  }}
                  onMouseOver={() => {
                    if (disabled) {
                      setDisabled(false)
                    }
                  }}
                  onMouseOut={() => {
                    setDisabled(true)
                  }}
                  // // fix eslintjsx-a11y/mouse-events-have-key-events
                  // // https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/master/docs/rules/mouse-events-have-key-events.md
                  // onFocus={() => {}}
                  // onBlur={() => {}}
                  // //end
                >
                  Création d'agent
                </div>
              }
              panelRef={modalRef}
              width={1000}
              open={open}
              onOk={handleOk}
              onCancel={handleCancel}
              // loading={loading}
              modalRender={modal => (
                <Draggable
                  disabled={disabled}
                  bounds={bounds}
                  nodeRef={draggleRef}
                  onStart={(event, uiData) => onStart(event, uiData)}
                >
                  <div ref={draggleRef}>{modal}</div>
                </Draggable>
              )}
              footer={submitter}
              // destroyOnClose
            >
              {dom}
            </Modal>
          )
        }}
      >
        <StepsForm.StepForm
          name="step1"
          title="Fiche de renseignement"
          // stepProps={{
          //   description: "C'est l'état civile",
          // }}
          onFinish={async val => {
            await waitTime(500)
            setCurrent(c => ++c)
            return true
          }}
          grid
        >
          <Tabs items={tabItems} />
        </StepsForm.StepForm>

        <StepsForm.StepForm
          name="step2"
          title="Situation Matrimoniale"
          // stepProps={{
          //   description: '这里填入运维参数',
          // }}
          onFinish={async () => {
            // console.log(restFormRef.current?.getFieldsValue())
            return true
          }}
          grid
        >
          <Enfants />
        </StepsForm.StepForm>
      </StepsForm>
    </div>
  )
}

export default AddAgents
