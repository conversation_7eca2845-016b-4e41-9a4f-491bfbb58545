import React from "react"
import { createBrowserRouter, Navigate, RouteObject } from "react-router"
import { BASENAME, HOME, PATH_ADMIN, PATH_AUTH, PATH_GUEST, REQUEST, SIGNIN, UPDATE_PASSWORD } from "./constants"
import { loadPage } from "./hocs"
import { AdminLayout, AuthLayout, GuestLayout } from "./layouts"
import ErrorPage from "./pages/ErrorPage"
import { typesPrimesLoader } from "./loaders/primeLoaders"


// Routes publiques
const publicRoutes: RouteObject[] = [
  {
    index: true,
    Component: loadPage(import("./pages/guest/HomePage")),
  },
  {
    path: HOME,
    Component: loadPage(import("./pages/guest/HomePage")),
  },
]

// Routes d'authentification
const authRoutes: RouteObject[] = [
  {
    index: true,
    element: React.createElement(Navigate, {to: PATH_AUTH.signin, replace: true}),
  },
  {
    path: SIGNIN,
    Component: loadPage(import("./pages/auth/SignInPage")),
  },
  {
    path: REQUEST,
    Component: loadPage(import("./pages/auth/RequestPage")),
  },
  {
    path: UPDATE_PASSWORD,
    Component: loadPage(import("./pages/auth/UpdatePasswordPage")),
  },
]

// Routes protégées (admin)
const protectedRoutes: RouteObject[] = [

  {
    index:true,
    Component: loadPage(import("./pages/admin/Home"), {withFooter: true}),
  },
  {
    path: PATH_ADMIN.convoyage,
    Component: loadPage(import("./pages/convoyage/ConvoyageAdminPage"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.utilisateurs,
    Component: loadPage(import("./pages/utilisateurs/UserPage"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.agents,
    Component: loadPage(import("./pages/agents"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.primes,
    children: [
      {
        index: true,
        Component: loadPage(import("./pages/primes"), undefined),
      },
      {
        path: "add/repartition",
        Component: loadPage(import("./pages/primes/add/AddRepartitionPage"), undefined),
      },
      {
        path: "edit/:id",
        Component: loadPage(import("./pages/primes/edit/[id]"), undefined),
      },
      {
        path: "typePrime",
        Component: loadPage(import("./pages/primes/typePrime"), undefined),
        loader: typesPrimesLoader,
      },
      {
        path: "repartition/:id",
        Component: loadPage(import("./pages/primes/repartition/RepartitionsPage"), undefined),
      },
      {
        path: "management",
        Component: loadPage(import("./pages/primes/management/PrimeManagementPage"), undefined),
      },
      {
        path: "coefficients",
        Component: loadPage(import("./pages/primes/coefficients/PrimeSelectionPage"), undefined),
      },
      {
        path: "coefficients/:primeId",
        Component: loadPage(import("./pages/primes/coefficients/CoefficientManagementPage"), undefined),
      }
    ],
  },
]

// Création du routeur
const router = createBrowserRouter(
  [
    {
      ErrorBoundary: ErrorPage,
      children: [
        {
          Component: GuestLayout,
          children: [
            ...publicRoutes,
          ]
        },
        {
          path: PATH_AUTH.root,
          Component: AuthLayout,
          children: [
            ...authRoutes,
          ]
        },
        {
          path: PATH_ADMIN.root,
          Component: AdminLayout,
          children: [
            {
              ErrorBoundary: ErrorPage,
              children: [
                ...protectedRoutes,
              ]
            },
          ]
        },
        {
          path: PATH_GUEST.login,
          element: React.createElement(Navigate, {to: PATH_AUTH.signin}),
        },
        {// Routes d'erreurs
          path: '/:id',
          Component: ErrorPage
        },
      ],
    }
  ],
  { basename: BASENAME }
)

export default router
