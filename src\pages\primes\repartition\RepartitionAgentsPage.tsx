import { useState, useEffect } from "react";
import { PageContainer } from "@ant-design/pro-components";
import {
  Button,
  Typography,
  Table,
  Space,
  Card,
  InputNumber,
  message,
  Spin,
  Statistic,
  Row,
  Col,
  Divider,
} from "antd";
import { ArrowLeftOutlined, SaveOutlined, CheckCircleOutlined } from "@ant-design/icons";
import { useNavigate, useParams } from "react-router";
import { Prime } from "~/models/Prime";
import { Agent, AgentRepartition } from "~/models/Agent";
import { getPrimeById } from "~/services/api/primeService";
import { getAgents, saveAgentRepartitions } from "~/services/api/agentService";

const RepartitionAgentsPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [prime, setPrime] = useState<Prime | null>(null);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [repartitions, setRepartitions] = useState<AgentRepartition[]>([]);
  const [totalCoefficient, setTotalCoefficient] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;
      
      setLoading(true);
      try {
        const [primeData, agentsData] = await Promise.all([
          getPrimeById(id),
          getAgents()
        ]);
        
        setPrime(primeData);
        setAgents(agentsData.data);
        
        // Initialiser les répartitions
        const initialRepartitions = agentsData.data.map(agent => ({
          agentId: agent.id,
          primeId: id,
          coefficient: agent.coefficient,
          montant: 0
        }));
        
        setRepartitions(initialRepartitions);
        
        // Calculer le total des coefficients
        const total = initialRepartitions.reduce((sum, rep) => sum + rep.coefficient, 0);
        setTotalCoefficient(total);
        
        // Calculer les montants en fonction des coefficients
        if (primeData.amount && total > 0) {
          calculateMontants(initialRepartitions, primeData.amount, total);
        }
      } catch (error) {
        message.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id]);

  const handleBack = () => {
    navigate("/primes");
  };

  const handleCoefficientChange = (agentId: string, value: number) => {
    const updatedRepartitions = repartitions.map(rep => 
      rep.agentId === agentId ? { ...rep, coefficient: value } : rep
    );
    
    setRepartitions(updatedRepartitions);
    
    // Recalculer le total des coefficients
    const total = updatedRepartitions.reduce((sum, rep) => sum + rep.coefficient, 0);
    setTotalCoefficient(total);
    
    // Recalculer les montants
    if (prime?.amount && total > 0) {
      calculateMontants(updatedRepartitions, prime.amount, total);
    }
  };

  const calculateMontants = (reps: AgentRepartition[], totalAmount: number, totalCoef: number) => {
    const updatedRepartitions = reps.map(rep => ({
      ...rep,
      montant: totalCoef > 0 ? (rep.coefficient / totalCoef) * totalAmount : 0
    }));
    
    setRepartitions(updatedRepartitions);
  };

  const handleSave = async () => {
    if (!id || !prime) return;
    
    setSaving(true);
    try {
      await saveAgentRepartitions(repartitions);
      message.success("Répartition enregistrée avec succès");
      navigate("/primes");
    } catch (error) {
      message.error("Erreur lors de l'enregistrement de la répartition");
    } finally {
      setSaving(false);
    }
  };

  const columns = [
    {
      title: "Matricule",
      dataIndex: "matricule",
      key: "matricule",
      render: (_: any, record: Agent) => {
        const agent = agents.find(a => a.id === record.id);
        return agent?.matricule || '';
      }
    },
    {
      title: "Nom",
      dataIndex: "nom",
      key: "nom",
      render: (_: any, record: Agent) => {
        const agent = agents.find(a => a.id === record.id);
        return agent?.nom || '';
      }
    },
    {
      title: "Prénom",
      dataIndex: "prenom",
      key: "prenom",
      render: (_: any, record: Agent) => {
        const agent = agents.find(a => a.id === record.id);
        return agent?.prenom || '';
      }
    },
    {
      title: "Grade",
      dataIndex: "grade",
      key: "grade",
      render: (_: any, record: Agent) => {
        const agent = agents.find(a => a.id === record.id);
        return agent?.grade || '';
      }
    },
    {
      title: "Unité",
      dataIndex: "unite",
      key: "unite",
      render: (_: any, record: Agent) => {
        const agent = agents.find(a => a.id === record.id);
        return agent?.unite || '';
      }
    },
    {
      title: "Coefficient",
      dataIndex: "coefficient",
      key: "coefficient",
      render: (_: any, record: Agent) => {
        const repartition = repartitions.find(r => r.agentId === record.id);
        return (
          <InputNumber
            value={repartition?.coefficient || 0}
            onChange={(value) => handleCoefficientChange(record.id, value || 0)}
            min={0}
            step={0.1}
            style={{ width: '100%' }}
          />
        );
      }
    },
    {
      title: "Montant",
      dataIndex: "montant",
      key: "montant",
      render: (_: any, record: Agent) => {
        const repartition = repartitions.find(r => r.agentId === record.id);
        return `${repartition?.montant.toLocaleString()} €`;
      }
    }
  ];

  return (
    <PageContainer
      header={{
        title: (
          <Typography.Title level={3} className="w-full">
            Répartition de la Prime
          </Typography.Title>
        ),
        onBack: handleBack,
        backIcon: <ArrowLeftOutlined />,
      }}
    >
      <Spin spinning={loading}>
        {prime && (
          <>
            <Card title="Informations de la Prime" style={{ marginBottom: 20 }}>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic title="Libellé" value={prime.name} />
                </Col>
                <Col span={6}>
                  <Statistic title="Type" value={prime.type} />
                </Col>
                <Col span={6}>
                  <Statistic title="Montant Total" value={prime.amount} suffix="€" />
                </Col>
                <Col span={6}>
                  <Statistic title="Période" value={prime.periode || '-'} />
                </Col>
              </Row>
              <Divider />
              <Typography.Paragraph>
                {prime.description || 'Aucune description'}
              </Typography.Paragraph>
            </Card>

            <Card title="Répartition des Agents" style={{ marginBottom: 20 }}>
              <Table
                dataSource={agents}
                columns={columns}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                footer={() => (
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Typography.Text strong>
                        Total des coefficients: {totalCoefficient.toFixed(2)}
                      </Typography.Text>
                    </Col>
                    <Col>
                      <Typography.Text strong>
                        Montant total à répartir: {prime.amount.toLocaleString()} €
                      </Typography.Text>
                    </Col>
                  </Row>
                )}
              />

              <div style={{ marginTop: 20, textAlign: 'right' }}>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSave}
                    loading={saving}
                  >
                    Enregistrer la répartition
                  </Button>
                  <Button onClick={handleBack}>Annuler</Button>
                </Space>
              </div>
            </Card>
          </>
        )}
      </Spin>
    </PageContainer>
  );
};

export default RepartitionAgentsPage;
