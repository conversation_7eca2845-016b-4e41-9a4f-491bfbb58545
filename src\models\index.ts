export type { ThemeMode } from "antd-style"

export type Local = "fr" | "en"

export interface AppResponse<T = any> {
  timestamp: string;
  status: string;
  error?: string;
  message?: string;
  data?: T[];
}

export interface User {
  email: string;
  role: string;
  permissions: string[];
}

export interface AuthState {
  token: string;
  isAuthenticated: boolean;
  user?: User;
}

export interface LoginPayload {
    email: string
    password: string
    remember: boolean
}
